from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app import service, schemas
from app.core import deps, response_model

router = APIRouter()


@router.post("/query", response_model=response_model.RenderBase[schemas.GuaResponse], summary="问卦接口")
async def query_gua(
    obj_in: schemas.GuaRequest,
    db: AsyncSession = Depends(deps.get_db),
    token: None = Depends(deps.check_rate_limit)  # Apply rate limiting
):
    """
    问卦接口，根据用户提问随机选择一个卦象并生成解释
    
    限制：每个用户每天最多调用3次
    """
    return await service.gua.query(obj_in.question)
