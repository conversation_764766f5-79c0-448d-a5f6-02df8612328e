#-*-encoding: utf-8 -*-

import time
from datetime import datetime
from loguru import logger
from fastapi import FastAPI, Request
from app.core.security import parse_token


def init_http(app: FastAPI):
    # process time
    @app.middleware("http")
    async def before_request(request: Request, call_next):
        start_time = time.time()
        # parse requests
        authorization = request.headers.get('authorization')
        # if login, get user id
        if authorization:
            request.state.payload = parse_token(
                authorization.replace("Bearer ", ""))
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time * 1000)
        return response

    @app.middleware("http")
    async def add_rate_limit_headers(request: Request, call_next):
        response = await call_next(request)
        
        # Add rate limit headers if available
        if hasattr(request.state, "rate_limit"):
            rate_info = request.state.rate_limit
            response.headers["X-RateLimit-Limit"] = str(rate_info["limit"])
            response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
            response.headers["X-RateLimit-Reset"] = rate_info["reset"].strftime("%Y-%m-%dT%H:%M:%SZ")
        
        return response
