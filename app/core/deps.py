# -*- encoding: utf-8 -*-

from typing import Async<PERSON>enerator
from fastapi import HTTPException, status, Request, Depends
from fastapi.security import OAuth2<PERSON>asswordBearer
from app.core.db_session import AsyncSessionLocal
from app.core.security import parse_token, decode_token
# from app.core.logger import upload_log
from datetime import datetime
from loguru import logger
from sqlalchemy.orm import Session
from fastapi import Request, HTTPException
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JWTError
from conf.settings import settings
from sqlalchemy.ext.asyncio import AsyncSession
from app import crud
import redis
from datetime import datetime, timedelta

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl="/v1/login/access-token")

# Redis client for rate limiting
redis_client = redis.Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    password=settings.REDIS_PSWD,
    decode_responses=True
)

async def get_db() -> AsyncGenerator:
    """数据库会话依赖注入"""
    async with AsyncSessionLocal() as session:
        yield session

class JWTBearer(HTTPBearer):

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super().__call__(
            request)
        if not credentials or credentials.scheme != "Bearer":
            raise HTTPException(status_code=403, detail="无效的认证信息")

        try:
            payload = jwt.decode(credentials.credentials,
                                 settings.SECRET_KEY,
                                 algorithms=[settings.ALGORITHM])
            request.state.user_id = int(payload["sub"])
        except JWTError:
            raise HTTPException(status_code=403, detail="令牌无效或已过期")

        return credentials.credentials


# async def get_current_user(request: Request,
#                            token: str = Depends(reusable_oauth2),
#                            db: Session = Depends(get_db)):
#     # paylaod
#     payload = decode_token(token=token)
#     if payload and payload.get('email'):
#         user = crud.user.get_by_filter_one(db, {"email": payload.get('email')})
#         if not user:
#             user = crud.user.create(
#                 db, {
#                     "hash_id": payload.get('sub'),
#                     "email": payload.get('email'),
#                     "username": payload.get('preferred_username'),
#                     "last_name": payload.get('family_name'),
#                     "first_name": payload.get('given_name'),
#                     "enabled": 1
#                 })
#     else:
#         payload = parse_token(token=token)
#         user = crud.user.get(db, id=payload.get('sub'))
#     if not payload or not user:
#         raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
#                             detail="Could not validate credentials")

#     allow_list = config_manager.get_allow_list()
#     if user.email not in allow_list:
#         raise HTTPException(status_code=status.HTTP_403_FORBIDDEN,
#                             detail="Access Denied")
#     try:
#         await upload_log(method=request.method,
#                          url=str(request.url),
#                          username=user.username,
#                          email=user.email)
#     except Exception as e:
#         logger.warning(f"upload log error: {e}")
#     return user
async def get_current_user(request: Request,
                           token: str = Depends(reusable_oauth2),
                           db: AsyncSession = Depends(get_db)):
    # 解析 token 获取身份信息
    payload = decode_token(token=token)
    if not payload:
        payload = parse_token(token=token)
    if not payload:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="Could not validate credentials")

    # 获取身份标识（identifier）和类型（identity_type）
    user_id = payload.get('sub')
    # identity_type = payload.get('identity_type', 'EMAIL')  # 默认为 EMAIL，可按需调整

    # 获取认证身份
    auth_identity = await crud.auth.get_user_by_id(db, user_id)
    if not auth_identity:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="身份不存在")

    # 验证身份（credential 在 token 中或通过外部输入提供）
    credential = payload.get('credential')  # 假设 credential 也在 token 中，或从别处获取
    if not valid:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="认证失败")

    # 获取用户
    user = await crud.auth.get_user_by_user_id(db, auth_identity)
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                            detail="无法获取关联用户")

    # 校验白名单
    allow_list = config_manager.get_allow_list()
    if user.email not in allow_list:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN,
                            detail="Access Denied")

    # 上传访问日志
    try:
        await upload_log(method=request.method,
                         url=str(request.url),
                         username=user.username,
                         email=user.email)
    except Exception as e:
        logger.warning(f"upload log error: {e}")

    return user

async def check_rate_limit(request: Request, 
                          token: str = Depends(JWTBearer()),
                          db: AsyncSession = Depends(get_db),
                          limit: int = 3,
                          period: int = 86400):  # 86400 seconds = 1 day
    """
    Rate limiting dependency - limits API calls to a specified number per day per user
    
    Args:
        request: The request object
        token: JWT token from the JWTBearer dependency
        db: Database session
        limit: Maximum number of calls allowed per period
        period: Time period in seconds (default: 1 day)
    
    Returns:
        None if within limits, raises HTTPException otherwise
    """
    try:
        # Get user ID from token
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id = payload.get("sub")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的用户身份"
            )
        
        # Create a key for this user and endpoint
        endpoint = request.url.path
        rate_key = f"rate_limit:{user_id}:{endpoint}"
        
        # Get current count from Redis
        current_count = redis_client.get(rate_key)
        
        if current_count is None:
            # First request today, set to 1 with expiry
            redis_client.setex(rate_key, period, 1)
        elif int(current_count) >= limit:
            # Exceeded limit
            ttl = redis_client.ttl(rate_key)
            reset_time = datetime.now() + timedelta(seconds=ttl)
            reset_time_str = reset_time.strftime("%Y-%m-%d %H:%M:%S")
            
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"已达到今日调用次数上限 ({limit}次/天)，请于 {reset_time_str} 后再试"
            )
        else:
            # Increment count
            redis_client.incr(rate_key)
            
        # Add headers to show rate limit info
        request.state.rate_limit = {
            "limit": limit,
            "remaining": limit - (int(current_count or 0) + 1),
            "reset": datetime.now() + timedelta(seconds=redis_client.ttl(rate_key) or period)
        }
        
    except redis.RedisError as e:
        # Log the error but don't block the request if Redis is down
        logger.error(f"Rate limiting error: {str(e)}")
    except Exception as e:
        logger.error(f"Rate limiting unexpected error: {str(e)}")
        # Don't block requests if there's an unexpected error
        
    return None


