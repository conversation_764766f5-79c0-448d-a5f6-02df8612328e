import requests
import random
import json
import os
from loguru import logger
from app.core import response_model, custom_exception
from conf.settings import settings

# API配置
API_KEY = settings.AI_API_KEY
BASE_URL = settings.AI_BASE_URL

class GuaService:
    def __init__(self):
        self.hex_db = self._load_hexagrams()
        
    def _load_hexagrams(self):
        """从JSON文件加载64卦数据"""
        try:
            # file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'gua.json')
            file_path = "conf/gua.json"
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载卦象数据失败: {str(e)}")
            # 返回一个简单的默认卦象，避免系统崩溃
            return [{
                "name": "乾卦",
                "symbol": "☰",
                "meaning": "天行健，君子以自强不息",
                "description": "乾为天，刚健中正"
            }]
    
    def _call_deepseek_api(self, prompt, model="deepseek-ai/DeepSeek-V3", temperature=0.7, max_tokens=2048):
        """调用DeepSeek API生成解释"""
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        try:
            response = requests.post(f"{BASE_URL}/chat/completions", headers=headers, json=payload)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API请求失败: {response.status_code}, {response.text}")
                raise custom_exception.ServiceError(message=f"AI服务暂时不可用")
        except Exception as e:
            logger.error(f"调用AI服务失败: {str(e)}")
            raise custom_exception.ServiceError(message="AI服务连接失败")
    
    def _generate_interpretation(self, hexagram, question):
        """生成卦象解释"""
        prompt = f"""
        [角色设定]你是一位精通易经的资深顾问
        [当前卦象]{hexagram['name']}（{hexagram['symbol']}）- {hexagram['meaning']}
        [卦象描述]{hexagram['description']}
        [核心任务]根据卦象分析用户问题，要求：
        1. 使用通俗易懂的术语
        2. 200字以内描述清楚
        3. 除占卜以外的任何问题不回答，比如讲故事、聊天、知识传授等等
        4. 不展示任何与占卜无关的内容
        用户问题：{question}"""
        
        try:
            response = self._call_deepseek_api(prompt, temperature=0.5, max_tokens=256)
            return response['choices'][0]['message']['content'].strip()
        except Exception as e:
            logger.error(f"生成解释失败: {str(e)}")
            return "无法生成解释，请稍后再试。"
    
    async def query(self, question):
        """处理问卦请求"""
        try:
            # 随机选择一个卦象
            hexagram = random.choice(self.hex_db)
            
            # 生成解释
            # interpretation = self._generate_interpretation(hexagram, question)
            interpretation = "for test"
            result = {
                "hexagram": hexagram,
                "interpretation": interpretation
            }
            
            return response_model.RenderBase(data=result)
        except Exception as e:
            logger.error(f"问卦处理失败: {str(e)}")
            raise custom_exception.ServiceError(message="问卦服务暂时不可用")


gua = GuaService()